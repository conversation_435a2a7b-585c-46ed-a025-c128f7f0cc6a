<!DOCTYPE html>
<html lang="ja">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>About Sasto Sulav Express - Our Story and Services</title>
  <meta name="description" content="Learn about Sasto Sulav Expreess, our journey, services, and commitment to excellence. Discover our diverse portfolio of businesses in Japan." />
  <meta name="keywords" content="sasto sulav, japanese business, nepali restaurant japan, import export japan, engineering services" />

  <!-- Favicon -->
  <link rel="apple-touch-icon" sizes="180x180" href="/assets/apple-touch-icon.png" />
  <link rel="icon" type="image/png" sizes="32x32" href="/assets/favicon-32x32.png" />
  <link rel="icon" type="image/png" sizes="16x16" href="/assets/favicon-16x16.png" />

  <!-- Open Graph / Social Media -->
  <meta property="og:type" content="website" />
  <meta property="og:title" content="About Sasto Sulav Express - Our Story and Services" />
  <meta property="og:description" content="Learn about our journey and commitment to excellence in Japan." />
  <meta property="og:image" content="/assets/logo.png" />
  <meta property="og:url" content="https://sastosulavexpress.com/about.html" />

  <!-- Twitter -->
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content="About Sasto Sulav Express - Our Story and Services" />
  <meta name="twitter:description" content="Learn about our journey and commitment to excellence in Japan." />
  <meta property="twitter:url" content="https://sastosulavexpress.com/about.html" />

  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  <link rel="stylesheet" href="/index.css" />
  <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
  <script src="/translations.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" />
</head>

<body class="font-dm-sans antialiased">
  <!-- =========== HEADER =============== -->
  <header class="flex justify-center z-30 sticky top-0 transition-all ease-in-out duration-200 pt-3">
    <nav id="nav" class="flex xl:w-[1200px] w-full items-center justify-between py-3 rounded-full pl-6 pr-3 shadow border border-[var(--border)] bg-[var(--background)]/50 backdrop-blur">
      <a href="/index.html" class="text-2xl font-bold relative">
        <div class="h-12 bg-[var(--primary)] w-10 absolute top-1/2 -translate-y-1/2 -left-4 rounded-l-full"></div>
        <p class="relative z-50">Sasto Sulav Express</p>
      </a>
      <ul class="lg:flex items-center font-medium text-sm hidden">
        <li>
          <a href="/sasto-sulav-express.html" class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase" data-i18n="sastoSulav">Sasto Sulav Express</a>
        </li>
        <li>
          <a href="/nepali-dining.html" class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase" data-i18n="nepaliDining">NEPALI Dining</a>
        </li>
        <li>
          <a href="/about.html" class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase" data-i18n="about">ABOUT</a>
        </li>
      </ul>
      <div class="flex items-center gap-x-4">
        <select id="languageSelect" class="bg-transparent border-none outline-none">
          <option value="ja">日本語</option>
          <option value="en">English</option>
        </select>
        <button id="openSidebar" class="lg:hidden block pr-4">
          <i class="fa fa-bars fa-2x" aria-hidden="true"></i>
        </button>
      </div>
    </nav>
  </header>

  <!-- ========= MOBILE SIDEBAR ========= -->
  <div class="sidebar inset-0 fixed z-40 min-h-screen hidden">
    <div class="bg-black/50 absolute inset-0 animate-fade-in"></div>
    <div class="bg-[var(--background)] min-h-screen md:w-[70vw] ml-auto w-[80vw] p-6 relative flex flex-col items-start justify-between">
      <div class="space-y-20 w-full">
        <div class="flex justify-between items-center">
          <h3 class="text-2xl font-bold">Sasto Sulav Express</h3>
          <button id="closeSidebar">
            <i class="fa fa-times fa-2x" aria-hidden="true"></i>
          </button>
        </div>
        <div class="flex flex-col items-start gap-y-3 w-full">
          <select id="mobileLangSelect" class="bg-transparent border border-[var(--border)] rounded-md p-2 w-full">
            <option value="ja">日本語</option>
            <option value="en">English</option>
          </select>
          <ul class="font-medium text-sm space-y-6" id="sidebarLinks">
            <li>
              <a data-i18n="sastoSulav" href="/sasto-sulav-express.html" class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase block w-full">Sasto Sulav Express</a>
            </li>
            <li>
              <a data-i18n="nepaliDining" href="/nepali-dining.html" class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase block w-full">NEPALI Dining</a>
            </li>
            <li>
              <a data-i18n="about" href="/about.html" class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase block w-full">ABOUT</a>
            </li>
          </ul>
        </div>
      </div>
      <button data-i18n="close" id="closeSidebarButton" class="py-2 px-4 rounded-md bg-[var(--accent)] w-full">
        CLOSE
      </button>
    </div>
  </div>

  <!-- =========== MAIN CONTENT =============== -->
  <div class="container">
    <article class="py-10 grid grid-cols-12 lg:gap-x-28 lg:gap-y-0 gap-y-16 items-stretch lg:min-h-[90dvh] h-auto">
      <section class="col-span-12 lg:col-span-6 lg:h-full sm:h-[30rem] h-[20rem] relative">
        <img src="https://images.unsplash.com/photo-1664575601786-b00156752b61?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="banner" class="h-full w-full object-cover" fill />
      </section>
      <div class="col-span-12 lg:col-span-6">
        <h1 class="text-4xl lg:text-6xl font-bold" data-i18n="sasto-sulav-express">
          Sasto Sulav Express
        </h1>
        <div class="lg:pt-24 pt-12 space-y-14">
          <section class="space-y-6">
            <div class="flex items-end gap-1">
              <div class="h-0.5 w-10 bg-[var(--primary)]"></div>
              <h3 class="text-2xl lg:text-4xl font-semibold" data-i18n="whoWeAre">
                Who are we?
              </h3>
            </div>
            <div>
              <p class="text-lg" data-i18n="whoWeAreDesc">
                Sasto Sulav Express is a dynamic multi-business enterprise
                established in Japan, bringing together diverse ventures under
                one umbrella. Founded with a vision to bridge cultures and
                create value, we've grown from a small family business into a
                respected name in various sectors including retail, dining,
                engineering, and import-export services.
              </p>
            </div>
          </section>

          <section class="space-y-6">
            <div class="flex items-end gap-1">
              <div class="h-0.5 w-10 bg-[var(--primary)]"></div>
              <h3 class="text-2xl lg:text-4xl font-semibold" data-i18n="whatWeDo">
                What we do?
              </h3>
            </div>
            <div>
              <p class="text-lg" data-i18n="whatWeDoDesc">
                We operate four distinct businesses: Sierra Japan Enterprises
                specializing in import-export, Nepali Dining offering
                authentic cuisine, Baral Engineering providing technical
                solutions, and Sasto Sulav Express delivering quality
                groceries. Each venture reflects our commitment to excellence
                and our goal of enriching the Japanese market with diverse,
                quality services.
              </p>
            </div>
          </section>

          <!-- =========== SOCIAL MEDIA LINKS =========== -->
          <section class="space-y-4">
            <h3 class="text-xl lg:text-2xl underline font-semibold" data-i18n="followUs">
              Follow us on
            </h3>
            <div class="flex flex-wrap items-center gap-3">
              <a target="_blank" class="h-10 w-10 flex items-center justify-center rounded-xl relative overflow-hidden group bg-blue-600 hover:bg-blue-800 transition-colors" href="">
                <div class="relative z-20">
                  <i class="fa fa-facebook"></i>
                </div>
              </a>
              <a target="_blank" class="h-10 w-10 flex items-center justify-center rounded-xl relative overflow-hidden group bg-pink-500 hover:bg-pink-700 transition-colors" href="">
                <div class="relative z-20">
                  <i class="fa fa-instagram"></i>
                </div>
              </a>
              <a target="_blank" class="h-10 w-10 flex items-center justify-center rounded-xl relative overflow-hidden group bg-blue-400 hover:bg-blue-600 transition-colors" href="">
                <div class="relative z-20">
                  <i class="fa fa-twitter"></i>
                </div>
              </a>
              <a target="_blank" class="h-10 w-10 flex items-center justify-center rounded-xl relative overflow-hidden group bg-red-600 hover:bg-red-800 transition-colors" href="">
                <div class="relative z-20">
                  <i class="fa fa-youtube"></i>
                </div>
              </a>
            </div>
          </section>
        </div>
      </div>
    </article>
  </div>

  <!-- ================ FOOTER ================ -->
  <footer id="footer" class="bg-[var(--muted)] py-4 text-center"></footer>

  <script src="https://unpkg.com/aos@next/dist/aos.js"></script>

  <script src="/script.js"></script>
</body>

</html>