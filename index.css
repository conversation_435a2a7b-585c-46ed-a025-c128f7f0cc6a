@import url("https://fonts.googleapis.com/css2?family=Atma:wght@300;400;500;600;700&family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&family=Lexend:wght@100..900&display=swap");

.font-dm-sans {
  font-family: "DM Sans", sans-serif;
}
.font-lexend {
  font-family: "Lexend", sans-serif;
}
.font-atma {
  font-family: "Atma", sans-serif;
}

html {
  scroll-behavior: smooth;
}

:root {
  --background: hsl(204 62% 100%);
  --foreground: hsl(204 54% 4%);
  --muted: hsl(204 40% 92%);
  --muted-foreground: hsl(204 12% 35%);
  --popover: hsl(204 62% 100%);
  --popover-foreground: hsl(204 54% 4%);
  --card: hsl(204 62% 100%);
  --card-foreground: hsl(204 54% 4%);
  --border: hsl(204 14% 94%);
  --input: hsl(204 14% 94%);
  --primary: hsl(204 59% 31%);
  --primary-foreground: hsl(204 59% 91%);
  --secondary: hsl(204 16% 89%);
  --secondary-foreground: hsl(204 16% 29%);
  --accent: hsl(204 26% 83%);
  --accent-foreground: hsl(204 26% 23%);
  --destructive: hsl(18 82% 44%);
  --destructive-foreground: hsl(0 0% 100%);
  --ring: hsl(204 59% 31%);
  --chart-1: hsl(204 59% 31%);
  --chart-2: hsl(204 16% 89%);
  --chart-3: hsl(204 26% 83%);
  --chart-4: hsl(204 16% 92%);
  --chart-5: hsl(204 62% 31%);
  --radius: 0.5rem;
}

html {
  font-family: "DM Sans", sans-serif;
}
/* .dark {
  --background: 204 51% 1%;
  --foreground: 204 40% 98%;
  --muted: 204 40% 8%;
  --muted-foreground: 204 12% 65%;
  --popover: 204 51% 1%;
  --popover-foreground: 204 40% 98%;
  --card: 204 51% 1%;
  --card-foreground: 204 40% 98%;
  --border: 204 14% 12%;
  --input: 204 14% 12%;
  --primary: 204 59% 31%;
  --primary-foreground: 204 59% 91%;
  --secondary: 204 8% 8%;
  --secondary-foreground: 204 8% 68%;
  --accent: 204 18% 18%;
  --accent-foreground: 204 18% 78%;
  --destructive: 18 82% 57%;
  --destructive-foreground: 0 0% 0%;
  --ring: 204 59% 31%;
  --chart-1: 204 59% 31%;
  --chart-2: 204 8% 8%;
  --chart-3: 204 18% 18%;
  --chart-4: 204 8% 11%;
  --chart-5: 204 62% 31%;
} */

.container {
  width: 100%;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 20px;
  padding-right: 20px;
}

@media (min-width: 1280px) {
  .container {
    width: 1200px;
    padding-left: 0;
    padding-right: 0;
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite linear;
}

.animate-shimmer-x {
  animation: shimmer-x 3s ease-in-out infinite;
}
@keyframes shimmer {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.animate-shimmer-shine {
  animation: shimmer-shine 0.75s forwards;
}

@keyframes shimmer-shine {
  100% {
    transform: translateX(100%);
  }
}

@keyframes shimmer-x {
  0% {
    background-position: 200% 0;
  }
  50% {
    background-position: 0 0;
  }
  100% {
    background-position: 200% 0;
  }
}
.embla {
  --slide-spacing: 1rem;
  --slide-size: 100%;
}

.embla__viewport {
  overflow: hidden;
}

.embla__container {
  backface-visibility: hidden;
  display: flex;
  touch-action: pan-y pinch-zoom;
}

.embla__slide {
  min-width: 0;
  flex: 0 0 var(--slide-size);
}

@media (min-width: 640px) {
  .embla {
    --slide-size: 50%;
  }
}

@media (min-width: 1024px) {
  .embla {
    --slide-size: 33.333333%;
  }
}

@media (min-width: 1280px) {
  .embla {
    --slide-size: 20.5%;
  }
}

.embla__progress {
  border-radius: 5rem;

  background-color: var(--background);
  position: relative;
  height: 0.5rem;
  justify-self: flex-end;
  align-self: center;
  width: 13rem;
  max-width: 90%;
  overflow: hidden;
  border: 1px solid var(--border);
}
.embla__progress__bar {
  background-color: var(--primary);
  position: absolute;
  width: 100%;
  top: 0;
  bottom: 0;
  left: -100%;
  border-radius: 5rem;
}
