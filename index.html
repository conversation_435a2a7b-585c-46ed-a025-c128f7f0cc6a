<!DOCTYPE html>
<html lang="ja">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Sasto Sulav Express - Your trusted groceries supplier</title>
    <meta
      name="description"
      content="Sasto Sulav Express is your trusted groceries supplier, providing a wide range of authentic South Asian groceries in Japan."
    />
    <meta name="keywords" content="Nepali Dining, Sasto Sulav Express" />
    <meta name="author" content="Sasto Sulav Express" />

    <!-- Favicon -->
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="/assets/apple-touch-icon.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="/assets/favicon-32x32.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="/assets/favicon-16x16.png"
    />
    <link rel="manifest" href="/assets/site.webmanifest" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://sastosulavexpress.com/" />
    <meta
      property="og:title"
      content="Sasto Sulav Express - Your trusted groceries supplier"
    />
    <meta
      property="og:description"
      content="Sasto Sulav Express is your trusted groceries supplier, providing a wide range of authentic South Asian groceries in Japan."
    />
    <meta property="og:image" content="/assets/home_mobile_bg.png" />
    <meta property="og:site_name" content="Sasto Sulav Express" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://sastosulavexpress.com/" />
    <meta
      property="twitter:title"
      content="Sasto Sulav Express - Your trusted groceries supplier"
    />
    <meta
      property="twitter:description"
      content="Sasto Sulav Express is your trusted groceries supplier, providing a wide range of authentic South Asian groceries in Japan."
    />
    <meta property="twitter:image" content="/assets/home_mobile_bg.png" />

    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link rel="stylesheet" href="/index.css" />
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
    <script src="/translations.js"></script>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css"
    />
  </head>

  <body class="font-dm-sans antialiased">
    <!-- =========== HEADER =============== -->
    <header
      class="md:flex hidden justify-center z-30 sticky top-0 transition-all ease-in-out duration-200 pt-3"
    >
      <nav
        id="nav"
        class="flex xl:w-[1200px] w-full items-center justify-between py-3 rounded-full pl-6 pr-3 shadow border border-[var(--border)] bg-[var(--background)]/50 backdrop-blur"
      >
        <a
          href="/index.html"
          class="text-2xl font-bold relative"
          data-i18n="sasto-sulav-express"
        >
          <div
            class="h-12 bg-[var(--primary)] w-10 absolute top-1/2 -translate-y-1/2 -left-4 rounded-l-full"
          ></div>
          <p class="relative z-50">Sasto Sulav Express</p>
        </a>
        <ul class="lg:flex items-center font-medium text-sm hidden">
          <li>
            <a
              href="/sasto-sulav-express.html"
              class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase"
              data-i18n="sastoSulav"
              >Sasto Sulav Express</a
            >
          </li>
          <li>
            <a
              href="/nepali-dining.html"
              class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase"
              data-i18n="nepaliDining"
              >NEPALI Dining</a
            >
          </li>
          <li>
            <a
              href="/about.html"
              class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase"
              data-i18n="about"
              >ABOUT</a
            >
          </li>
        </ul>
        <div class="flex items-center gap-x-4">
          <select
            id="languageSelect"
            class="bg-transparent border-none outline-none"
          >
            <option value="ja">日本語</option>
            <option value="en">English</option>
          </select>
          <button id="openSidebar" class="lg:hidden block pr-4">
            <i class="fa fa-bars fa-2x" aria-hidden="true"></i>
          </button>
        </div>
      </nav>
    </header>
    <!-- ========= MOBILE SIDEBAR ========= -->
    <div class="sidebar inset-0 fixed z-40 min-h-screen hidden">
      <div class="bg-black/50 absolute inset-0 animate-fade-in"></div>
      <div
        class="bg-[var(--background)] min-h-screen md:w-[70vw] ml-auto w-[80vw] p-6 relative flex flex-col items-start justify-between"
      >
        <div class="space-y-20 w-full">
          <div class="flex justify-between items-center">
            <a href="/index.html" class="text-2xl font-bold"
              >Sasto Sulav Express</a
            >
            <button id="closeSidebar">
              <i class="fa fa-times fa-2x" aria-hidden="true"></i>
            </button>
          </div>
          <div class="flex flex-col items-start gap-y-3 w-full">
            <select
              id="mobileLangSelect"
              class="bg-transparent border border-[var(--border)] rounded-md p-2 w-full"
            >
              <option value="ja">日本語</option>
              <option value="en">English</option>
            </select>
            <ul class="font-medium text-sm space-y-6" id="sidebarLinks">
              <li>
                <a
                  href="/sasto-sulav-express.html"
                  class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase block w-full"
                  data-i18n="sastoSulav"
                  >Sasto Sulav Express</a
                >
              </li>
              <li>
                <a
                  href="/nepali-dining.html"
                  class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase block w-full"
                  data-i18n="nepaliDining"
                  >NEPALI Dining</a
                >
              </li>
              <li>
                <a
                  href="/about.html"
                  class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase block w-full"
                  data-i18n="about"
                  >ABOUT</a
                >
              </li>
            </ul>
          </div>
        </div>
        <button
          id="closeSidebarButton"
          class="py-2 px-4 rounded-md bg-[var(--accent)] w-full"
          data-i18n="close"
        >
          CLOSE
        </button>
      </div>
    </div>
    <main class="overflow-x-hidden">
      <!-- ============ HOME BANNER MOBILE ============ -->
      <section
        class="md:min-h-auto md:hidden flex flex-col justify-between min-h-screen bg-[url(/assets/home_mobile_bg.png)] md:bg-none bg-cover bg-center bg-no-repeat"
      >
        <div class="flex items-center justify-between p-5">
          <h1
            class="text-5xl font-bold font-lexend leading-[60px] text-[var(--accent)]"
          >
            Sasto Sualav <br />
            <span class="text-[var(--accent-foreground)]">Express</span>
          </h1>
          <button id="homeSidebarButton" class="lg:hidden block">
            <i class="fa fa-columns fa-2x text-white" aria-hidden="true"></i>
          </button>
        </div>
        <div class="p-5">
          <div class="border border-[var(--border)] rounded-3xl p-6">
            <h3
              class="text-center text-3xl font-semibold font-atma text-[var(--muted)]"
              data-aos="zoom-out"
              data-i18n="servingCommunity"
            >
              Serving the community for years.
            </h3>
          </div>
          <div class="pt-20" data-aos="fade-up" data-aos-delay="100">
            <div>
              <img
                src="/assets/home_mobile_banner.png"
                alt="engineers_talking"
                class="w-full aspect-video object-contain"
              />
            </div>
            <div class="flex justify-center pt-3">
              <a
                data-i18n="exploreMore"
                href="#sasto-info"
                class="rounded-full bg-gradient-to-r from-[var(--primary)] to-[#6192B3] text-white px-4 py-2"
              >
                Explore more
              </a>
            </div>
          </div>
        </div>
      </section>
      <!-- =============== HOME CONTENT WITH IMAGES AND TEXT ============== -->
      <article class="space-y-14 bg-[var(--secondary)] py-20">
        <section
          class="container lg:h-[50rem] grid grid-cols-12 lg:gap-x-8 lg:gap-y-0 gap-y-10 items-center"
          id="sasto-info"
        >
          <div
            class="col-span-12 lg:col-span-6 space-y-10 lg:order-1 order-2"
            data-aos="fade-up"
          >
            <div class="space-y-6">
              <h3
                class="lg:text-5xl text-4xl font-semibold"
                data-i18n="sastoSulav"
              >
                Sasto Sulav Express
              </h3>
              <p data-i18n="sastoSulavDesc">
                Your premier destination for authentic South Asian groceries in
                Japan. We bring the flavors of Nepal and South Asia directly to
                your doorstep, offering a wide selection of spices, lentils,
                rice, and traditional ingredients. Our express delivery service
                ensures fresh and quality products reach you quickly.
              </p>
            </div>
            <div>
              <a href="sasto-sulav-express.html">
                <button
                  class="group relative h-[calc(48px+8px)] rounded-full border border-[var(--primary)] py-1 pr-14 font-medium bg-[var(--secondary)] text-[var(--secondary-foreground)] shadow-sm flex items-center px-4"
                >
                  <span
                    data-i18n="knowMore"
                    class="z-10 pr-2 group-hover:text-white capitalize"
                  >
                    Know More
                  </span>
                  <div
                    class="absolute right-1 inline-flex h-12 w-12 items-center justify-end rounded-full bg-neutral-700 transition-[width] group-hover:w-[calc(100%-8px)]"
                  >
                    <div class="mr-3.5 flex items-center justify-center">
                      <svg
                        width="15"
                        height="15"
                        viewBox="0 0 15 15"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        class="w-5 h-5 text-neutral-50"
                      >
                        <path
                          d="M8.14645 3.14645C8.34171 2.95118 8.65829 2.95118 8.85355 3.14645L12.8536 7.14645C13.0488 7.34171 13.0488 7.65829 12.8536 7.85355L8.85355 11.8536C8.65829 12.0488 8.34171 12.0488 8.14645 11.8536C7.95118 11.6583 7.95118 11.3417 8.14645 11.1464L11.2929 8H2.5C2.22386 8 2 7.77614 2 7.5C2 7.22386 2.22386 7 2.5 7H11.2929L8.14645 3.85355C7.95118 3.65829 7.95118 3.34171 8.14645 3.14645Z"
                          fill="currentColor"
                          fillRule="evenodd"
                          clipRule="evenodd"
                        ></path>
                      </svg>
                    </div>
                  </div>
                </button>
              </a>
            </div>
          </div>
          <div
            class="col-span-12 lg:col-span-6 lg:h-full md:h-[50dvh] h-[40dvh] overflow-hidden p-0 relative lg:order-2 order-1"
            data-aos="fade-left"
          >
            <img
              src="https://plus.unsplash.com/premium_photo-1664551734602-49640bd82eba?q=80&w=1976&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
              alt="sasto-sulav-express"
              class="h-full w-full object-cover"
            />
          </div>
        </section>
        <section
          class="container lg:h-[50rem] grid grid-cols-12 lg:gap-x-8 lg:gap-y-0 gap-y-10 items-center"
        >
          <div
            class="col-span-12 lg:col-span-6 lg:h-full md:h-[50dvh] h-[40dvh] overflow-hidden p-0 relative"
            data-aos="fade-right"
          >
            <img
              src="https://images.unsplash.com/photo-1504598318550-17eba1008a68?q=80&w=1972&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
              alt="nepali-dining"
              class="h-full w-full object-cover"
            />
          </div>
          <div class="col-span-12 lg:col-span-6 space-y-10" data-aos="fade-up">
            <div class="space-y-6">
              <h3
                class="lg:text-5xl text-4xl font-semibold"
                data-i18n="nepaliDining"
              >
                Nepali Dining
              </h3>
              <p data-i18n="nepaliDiningDesc">
                Experience the authentic taste of Nepal in the heart of Japan.
                Our restaurant serves traditional Nepali cuisine prepared by
                expert chefs, offering a perfect blend of flavors and spices.
                From classic momos to rich curries, we bring the warmth and
                hospitality of Nepal to your dining experience.
              </p>
            </div>
            <div>
              <a href="nepali-dining.html">
                <button
                  class="group relative h-[calc(48px+8px)] rounded-full border border-[var(--primary)] py-1 pr-14 font-medium bg-[var(--secondary)] text-[var(--secondary-foreground)] shadow-sm flex items-center px-4"
                >
                  <span
                    data-i18n="knowMore"
                    class="z-10 pr-2 group-hover:text-white capitalize"
                  >
                    Know More
                  </span>
                  <div
                    class="absolute right-1 inline-flex h-12 w-12 items-center justify-end rounded-full bg-neutral-700 transition-[width] group-hover:w-[calc(100%-8px)]"
                  >
                    <div class="mr-3.5 flex items-center justify-center">
                      <svg
                        width="15"
                        height="15"
                        viewBox="0 0 15 15"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        class="w-5 h-5 text-neutral-50"
                      >
                        <path
                          d="M8.14645 3.14645C8.34171 2.95118 8.65829 2.95118 8.85355 3.14645L12.8536 7.14645C13.0488 7.34171 13.0488 7.65829 12.8536 7.85355L8.85355 11.8536C8.65829 12.0488 8.34171 12.0488 8.14645 11.8536C7.95118 11.6583 7.95118 11.3417 8.14645 11.1464L11.2929 8H2.5C2.22386 8 2 7.77614 2 7.5C2 7.22386 2.22386 7 2.5 7H11.2929L8.14645 3.85355C7.95118 3.65829 7.95118 3.34171 8.14645 3.14645Z"
                          fill="currentColor"
                          fillRule="evenodd"
                          clipRule="evenodd"
                        ></path>
                      </svg>
                    </div>
                  </div>
                </button>
              </a>
            </div>
          </div>
        </section>
      </article>
    </main>
    <!-- ================ FOOTER ================ -->
    <footer id="footer" class="bg-[var(--muted)] py-4 text-center"></footer>
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script src="/script.js"></script>
  </body>
</html>
