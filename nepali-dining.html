<!DOCTYPE html>
<html lang="ja">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Nepali Dining - Authentic Nepali Cuisine in Japan</title>
  <meta name="description" content="Experience authentic Nepali cuisine at our family-owned restaurant in Japan. Enjoy traditional dishes, warm atmosphere, and exceptional service." />
  <meta name="keywords" content="nepali food, nepali restaurant japan, authentic nepali cuisine, baral brothers, japanese nepali restaurant" />

  <!-- Favicon -->
  <link rel="apple-touch-icon" sizes="180x180" href="/assets/apple-touch-icon.png" />
  <link rel="icon" type="image/png" sizes="32x32" href="/assets/favicon-32x32.png" />
  <link rel="icon" type="image/png" sizes="16x16" href="/assets/favicon-16x16.png" />

  <!-- Open Graph / Social Media -->
  <meta property="og:type" content="website" />
  <meta property="og:title" content="Nepali Dining - Authentic Nepali Cuisine in Japan" />
  <meta property="og:description" content="Experience authentic Nepali cuisine at our family-owned restaurant in Japan." />
  <meta property="og:image" content="/assets/daal_bhaat.webp" />
  <meta property="og:url" content="https://sastosulavexpress.com/nepali-dining.html" />

  <!-- Twitter -->
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content="Nepali Dining - Authentic Nepali Cuisine in Japan" />
  <meta name="twitter:description" content="Experience authentic Nepali cuisine at our family-owned restaurant in Japan." />
  <meta property="twitter:url" content="https://sastosulavexpress.com/nepali-dining.html" />

  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  <link rel="stylesheet" href="/index.css" />
  <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" />
  <script src="https://unpkg.com/embla-carousel/embla-carousel.umd.js"></script>
  <script src="https://unpkg.com/embla-carousel-autoplay/embla-carousel-autoplay.umd.js"></script>
  <script src="/translations.js"></script>
</head>

<body class="font-dm-sans antialiased">
  <!-- =========== HEADER =============== -->
  <header class="flex justify-center z-30 sticky top-0 transition-all ease-in-out duration-200 pt-3">
    <nav id="nav" class="flex xl:w-[1200px] w-full items-center justify-between py-3 rounded-full bg-[var(--background)]/50 backdrop-blur pl-6 pr-3">
      <a href="/index.html" class="text-2xl font-bold relative">
        <div class="h-12 bg-[var(--primary)] w-10 absolute top-1/2 -translate-y-1/2 -left-4 rounded-l-full"></div>
        <p class="relative z-50">Sasto Sulav Express</p>
      </a>
      <ul class="lg:flex items-center font-medium text-sm hidden">
        <li>
          <a href="/sasto-sulav-express.html" class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase" data-i18n="sastoSulav">Sasto Sulav Express</a>
        </li>
        <li>
          <a href="/nepali-dining.html" class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase" data-i18n="nepaliDining">NEPALI Dining</a>
        </li>
        <li>
          <a href="/about.html" class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase" data-i18n="about">ABOUT</a>
        </li>
      </ul>
      <div class="flex items-center gap-x-4">
        <select id="languageSelect" class="bg-transparent border-none outline-none">
          <option value="ja">日本語</option>
          <option value="en">English</option>
        </select>
        <button id="openSidebar" class="lg:hidden block pr-4">
          <i class="fa fa-bars fa-2x" aria-hidden="true"></i>
        </button>
      </div>
    </nav>
  </header>
  <main>
    <!-- ============= PAGE BANNER ============= -->
    <section class="lg:grid flex justify-center grid-cols-12 items-center gap-x-6 relative min-h-[83dvh] lg:bg-none bg-[url(/assets/daal_bhaat.webp)] bg-contain bg-center bg-no-repeat">
      <div class="col-span-12 lg:col-span-6 relative">
        <div class="relative lg:pl-20 lg:bg-yellow-300 py-6">
          <h1 class="lg:text-9xl text-8xl font-medium font-atma font-lexend lg:block flex flex-col items-center" data-aos="slide-right">
            <span class="flex items-end gap-x-3">
              <span data-i18n="nepali">Nepali</span>
              <img src="/assets/momos.webp" alt="momo" class="object-contain size-20 md:inline-block hidden" />
            </span>
            <span data-i18n="cuisines">Cuisines</span>
          </h1>
        </div>
      </div>
      <div class="col-span-12 lg:col-span-6 lg:block hidden">
        <img src="/assets/daal_bhaat.webp" class="aspect-square md:object-cover object-contain" alt="daal-bhaat" data-aos="zoom-in" />
      </div>
      <a href="#menu" class="rounded-full bg-gradient-to-r from-[var(--primary)] to-[#6192B3] px-4 py-2 absolute bottom-0 left-1/2 -translate-x-1/2 lg:hidden block" data-i18n="seeMenu">
        see menu
      </a>
    </section>

    <div class="container pt-20 space-y-36 pb-20">
      <!-- =========== FACILITIES =========== -->
      <section class="space-y-10">
        <h3 class="text-5xl font-medium text-center" data-i18n="ourFacilities">
          Our Facilities
        </h3>
        <div class="flex items-center lg:flex-nowrap flex-wrap gap-6 justify-center">
          <div class="flex flex-col items-center gap-y-3">
            <img class="size-40 rounded-full object-contain" src="/assets/hot_deals.png" alt="hot deals" />
            <div class="text-center space-y-1">
              <h6 class="text-xl font-medium" data-i18n="hotDeals">
                Hot Deals & Offers
              </h6>
              <p class="text-sm md:max-w-52" data-i18n="hotDealsDesc">
                We always have hot deals and offers for our customers.
              </p>
            </div>
          </div>
          <div class="flex flex-col items-center gap-y-3">
            <img class="size-40 rounded-full object-contain" src="/assets/chef.png" alt="amazing food" />
            <div class="text-center space-y-1">
              <h6 class="text-xl font-medium" data-i18n="amazingFood">
                Amazing Food
              </h6>
              <p class="text-sm md:max-w-52" data-i18n="amazingFoodDesc">
                We serve high-quality authentic Nepali cuisine.
              </p>
            </div>
          </div>
          <div class="flex flex-col items-center gap-y-3">
            <img class="size-40 rounded-full object-contain" src="/assets/delivery_man.png" alt="delivery man" />
            <div class="text-center space-y-1">
              <h6 class="text-xl font-medium" data-i18n="fastDelivery">
                Fast Delivery
              </h6>
              <p class="text-sm md:max-w-52" data-i18n="fastDeliveryDesc">
                Quick and reliable delivery service to your doorstep.
              </p>
            </div>
          </div>
        </div>
      </section>

      <!-- =========== GALLERY =========== -->
      <section class="space-y-10">
        <h3 class="text-5xl font-medium text-center" data-i18n="gallery">
          Gallery
        </h3>
        <div class="embla overflow-hidden relative">
          <div class="embla__viewport">
            <div class="embla__container flex gap-4">
              <div class="flex-[0_0_80%]">
                <img src="https://plus.unsplash.com/premium_photo-1661883237884-263e8de8869b?q=80&w=1778&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="" />
              </div>
              <div class="flex-[0_0_80%]">
                <img src="https://plus.unsplash.com/premium_photo-1661883237884-263e8de8869b?q=80&w=1778&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="" />
              </div>
              <div class="flex-[0_0_80%]">
                <img src="https://plus.unsplash.com/premium_photo-1661883237884-263e8de8869b?q=80&w=1778&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="" />
              </div>
              <div class="flex-[0_0_80%]">
                <img src="https://plus.unsplash.com/premium_photo-1661883237884-263e8de8869b?q=80&w=1778&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="" />
              </div>
              <div class="flex-[0_0_80%]">
                <img src="https://plus.unsplash.com/premium_photo-1661883237884-263e8de8869b?q=80&w=1778&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="" />
              </div>
              <div class="flex-[0_0_80%]">
                <img src="https://plus.unsplash.com/premium_photo-1661883237884-263e8de8869b?q=80&w=1778&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="" />
              </div>
            </div>
          </div>

          <!-- ========== CAROUSEL PROGRESS ========= -->
          <div class="flex justify-center pt-4">
            <div class="embla__progress">
              <div class="embla__progress__bar" style="transform: translate3d(0%, 0px, 0px)"></div>
            </div>
          </div>
        </div>
      </section>

      <!-- =========== MENU =========== -->

      <div id="menu" class="mx-auto max-w-4xl rounded-xl shadow-sm overflow-hidden">
        <header class="text-center py-10 px-6 border-b border-b-[var(--border)]">
          <h1 class="text-4xl md:text-5xl font-bold tracking-tight mb-2" data-i18n="ourMenu">
            Our Menu
          </h1>
          <p class="text-gray-500" data-i18n="seasonal">
            Seasonal & Locally Sourced Ingredients
          </p>
        </header>

        <div class="p-6 md:p-10">
          <section>
            <h2 class="text-2xl font-semibold mb-6">Appetizers</h2>
            <div class="space-y-6">
              <div class="flex flex-col md:flex-row md:justify-between md:items-baseline group">
                <div class="flex-1">
                  <h3 class="font-medium text-lg group-hover:text-gray-700 transition-colors">
                    Truffle Arancini
                  </h3>
                  <p class="text-gray-500 mt-1 text-sm">
                    Crispy risotto balls with black truffle and mozzarella
                  </p>
                </div>
                <div class="mt-1 md:mt-0 md:ml-4 font-medium text-gray-900">
                  $14
                </div>
              </div>
              <div class="flex flex-col md:flex-row md:justify-between md:items-baseline group">
                <div class="flex-1">
                  <h3 class="font-medium text-lg group-hover:text-gray-700 transition-colors">
                    Truffle Arancini
                  </h3>
                  <p class="text-gray-500 mt-1 text-sm">
                    Crispy risotto balls with black truffle and mozzarella
                  </p>
                </div>
                <div class="mt-1 md:mt-0 md:ml-4 font-medium text-gray-900">
                  $14
                </div>
              </div>
              <div class="flex flex-col md:flex-row md:justify-between md:items-baseline group">
                <div class="flex-1">
                  <h3 class="font-medium text-lg group-hover:text-gray-700 transition-colors">
                    Truffle Arancini
                  </h3>
                  <p class="text-gray-500 mt-1 text-sm">
                    Crispy risotto balls with black truffle and mozzarella
                  </p>
                </div>
                <div class="mt-1 md:mt-0 md:ml-4 font-medium text-gray-900">
                  $14
                </div>
              </div>
              <div class="flex flex-col md:flex-row md:justify-between md:items-baseline group">
                <div class="flex-1">
                  <h3 class="font-medium text-lg group-hover:text-gray-700 transition-colors">
                    Truffle Arancini
                  </h3>
                  <p class="text-gray-500 mt-1 text-sm">
                    Crispy risotto balls with black truffle and mozzarella
                  </p>
                </div>
                <div class="mt-1 md:mt-0 md:ml-4 font-medium text-gray-900">
                  $14
                </div>
              </div>
            </div>
          </section>
          <div class="border-b border-b-[var(--border)] my-10 w-full"></div>
          <section>
            <h2 class="text-2xl font-semibold mb-6">Appetizers</h2>
            <div class="space-y-6">
              <div class="flex flex-col md:flex-row md:justify-between md:items-baseline group">
                <div class="flex-1">
                  <h3 class="font-medium text-lg group-hover:text-gray-700 transition-colors">
                    Truffle Arancini
                  </h3>
                  <p class="text-gray-500 mt-1 text-sm">
                    Crispy risotto balls with black truffle and mozzarella
                  </p>
                </div>
                <div class="mt-1 md:mt-0 md:ml-4 font-medium text-gray-900">
                  $14
                </div>
              </div>
              <div class="flex flex-col md:flex-row md:justify-between md:items-baseline group">
                <div class="flex-1">
                  <h3 class="font-medium text-lg group-hover:text-gray-700 transition-colors">
                    Truffle Arancini
                  </h3>
                  <p class="text-gray-500 mt-1 text-sm">
                    Crispy risotto balls with black truffle and mozzarella
                  </p>
                </div>
                <div class="mt-1 md:mt-0 md:ml-4 font-medium text-gray-900">
                  $14
                </div>
              </div>
              <div class="flex flex-col md:flex-row md:justify-between md:items-baseline group">
                <div class="flex-1">
                  <h3 class="font-medium text-lg group-hover:text-gray-700 transition-colors">
                    Truffle Arancini
                  </h3>
                  <p class="text-gray-500 mt-1 text-sm">
                    Crispy risotto balls with black truffle and mozzarella
                  </p>
                </div>
                <div class="mt-1 md:mt-0 md:ml-4 font-medium text-gray-900">
                  $14
                </div>
              </div>
              <div class="flex flex-col md:flex-row md:justify-between md:items-baseline group">
                <div class="flex-1">
                  <h3 class="font-medium text-lg group-hover:text-gray-700 transition-colors">
                    Truffle Arancini
                  </h3>
                  <p class="text-gray-500 mt-1 text-sm">
                    Crispy risotto balls with black truffle and mozzarella
                  </p>
                </div>
                <div class="mt-1 md:mt-0 md:ml-4 font-medium text-gray-900">
                  $14
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>

      <!-- =========== ABOUT =========== -->
      <article class="space-y-10">
        <h3 class="text-5xl font-medium text-center" data-i18n="aboutUs">
          About Us
        </h3>
        <p class="text-center text-lg" data-i18n="aboutDescription">
          We are a family-owned restaurant that has been serving authentic
          Nepali cuisine for over 20 years. Our mission is to provide our
          customers with a warm and welcoming atmosphere, delicious food, and
          exceptional service. We take pride in using only the freshest
          ingredients and traditional cooking methods to create our dishes.
          Whether you're looking for a quick bite or a sit-down meal, we hope
          you'll come and experience the flavors of Nepal with us. Thank you
          for choosing us!
        </p>
      </article>
    </div>
  </main>
  <!-- ========= MOBILE SIDEBAR ========= -->
  <div class="sidebar inset-0 fixed z-40 min-h-screen hidden">
    <div class="bg-black/50 absolute inset-0 animate-fade-in"></div>
    <div class="bg-[var(--background)] min-h-screen md:w-[70vw] ml-auto w-[80vw] p-6 relative flex flex-col items-start justify-between">
      <div class="space-y-20 w-full">
        <div class="flex justify-between items-center">
          <a href="/index.html" class="text-2xl font-bold">Sasto Sulav Express</a>
          <button id="closeSidebar">
            <i class="fa fa-times fa-2x" aria-hidden="true"></i>
          </button>
        </div>
        <div class="flex flex-col items-start gap-y-3 w-full">
          <select id="mobileLangSelect" class="bg-transparent border border-[var(--border)] rounded-md p-2 w-full">
            <option value="ja">日本語</option>
            <option value="en">English</option>
          </select>
          <ul class="font-medium text-sm space-y-6" id="sidebarLinks">
            <li>
              <a data-i18n="sastoSulav" href="/sasto-sulav-express.html" class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase block w-full">Sasto Sulav Express</a>
            </li>
            <li>
              <a data-i18n="nepaliDining" href="/nepali-dining.html" class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase block w-full">NEPALI Dining</a>
            </li>
            <li>
              <a data-i18n="about" href="/about.html" class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase block w-full">ABOUT</a>
            </li>
          </ul>
        </div>
      </div>
      <button data-i18n="close" id="closeSidebarButton" class="py-2 px-4 rounded-md bg-[var(--accent)] w-full">
        CLOSE
      </button>
    </div>
  </div>

  <!-- =========== FOOTER =============== -->
  <footer id="footer" class="bg-[var(--muted)] py-4 text-center"></footer>

  <script src="https://unpkg.com/aos@next/dist/aos.js"></script>

  <script>
    const emblaNode = document.querySelector(".embla");
    const options = {
      loop: false,
      dragFree: true,
      autoplay: {
        delay: 0
      },
      containScroll: false,
      align: "start",
    };

    const viewportNode = emblaNode.querySelector(".embla__viewport");
    const plugins = [EmblaCarouselAutoplay()];
    const emblaApi = EmblaCarousel(viewportNode, options, plugins);

    const progressNode = emblaNode.querySelector(".embla__progress__bar");

    const updateProgress = () => {
      const progress = Math.max(0, Math.min(1, emblaApi.scrollProgress()));
      progressNode.style.transform = `translateX(${progress * 100}%)`;
    };

    emblaApi.on("scroll", updateProgress);
    emblaApi.on("reInit", updateProgress);
  </script>

  <script src="/script.js"></script>
</body>

</html>