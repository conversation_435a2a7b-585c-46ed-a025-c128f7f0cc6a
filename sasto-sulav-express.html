<!DOCTYPE html>
<html lang="ja">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Sasto Sulav Express - Fast & Reliable Delivery Services</title>
  <meta name="description" content="Sasto Sulav Express offers fast and reliable delivery services. Quick deliveries, real-time tracking, and professional service at competitive rates." />
  <meta name="keywords" content="delivery service, express delivery, fast delivery japan, sasto sulav express, courier service" />

  <!-- Favicon -->
  <link rel="apple-touch-icon" sizes="180x180" href="/assets/apple-touch-icon.png" />
  <link rel="icon" type="image/png" sizes="32x32" href="/assets/favicon-32x32.png" />
  <link rel="icon" type="image/png" sizes="16x16" href="/assets/favicon-16x16.png" />

  <!-- Open Graph / Social Media -->
  <meta property="og:type" content="website" />
  <meta property="og:title" content="Sasto Sulav Express - Fast & Reliable Delivery" />
  <meta property="og:description" content="Fast and reliable delivery services at competitive rates." />
  <meta property="og:image" content="/assets/logo.png" />
  <meta property="og:url" content="https://sastosulavexpress.com/sasto-sulav-express.html" />

  <!-- Twitter -->
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content="Sasto Sulav Express - Fast & Reliable Delivery" />
  <meta name="twitter:description" content="Fast and reliable delivery services at competitive rates." />
  <meta property="twitter:url" content="https://sastosulavexpress.com/sasto-sulav-express.html" />
  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
  <link rel="stylesheet" href="/index.css" />
  <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
  <script src="/translations.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" />
  <script src="https://unpkg.com/embla-carousel/embla-carousel.umd.js"></script>
  <script src="https://unpkg.com/embla-carousel-autoplay/embla-carousel-autoplay.umd.js"></script>
</head>

<body class="font-dm-sans antialiased">
  <!-- =========== HEADER =============== -->
  <header class="flex justify-center z-30 sticky top-0 transition-all ease-in-out duration-200 pt-3">
    <nav id="nav" class="flex xl:w-[1200px] w-full items-center justify-between py-3 rounded-full pl-6 pr-3 shadow border border-[var(--border)] bg-[var(--background)]/50 backdrop-blur">
      <a href="/index.html" class="text-2xl font-bold relative">
        <div class="h-12 bg-[var(--primary)] w-10 absolute top-1/2 -translate-y-1/2 -left-4 rounded-l-full"></div>
        <p class="relative z-50">Sasto Sulav Express</p>
      </a>
      <ul class="lg:flex items-center font-medium text-sm hidden">
        <li>
          <a href="/sasto-sulav-express.html" class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase" data-i18n="sastoSulav">Sasto Sulav Express</a>
        </li>
        <li>
          <a href="/nepali-dining.html" class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase" data-i18n="nepaliDining">NEPALI Dining</a>
        </li>
        <li>
          <a href="/about.html" class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase" data-i18n="about">ABOUT</a>
        </li>
      </ul>
      <div class="flex items-center gap-x-4">
        <select id="languageSelect" class="bg-transparent border-none outline-none">
          <option value="ja">日本語</option>
          <option value="en">English</option>
        </select>
        <button id="openSidebar" class="lg:hidden block pr-4">
          <i class="fa fa-bars fa-2x" aria-hidden="true"></i>
        </button>
      </div>
    </nav>
  </header>

  <!-- ========= MOBILE SIDEBAR ========= -->
  <div class="sidebar inset-0 fixed z-40 min-h-screen hidden">
    <div class="bg-black/50 absolute inset-0 animate-fade-in"></div>
    <div class="bg-[var(--background)] min-h-screen md:w-[70vw] ml-auto w-[80vw] p-6 relative flex flex-col items-start justify-between">
      <div class="space-y-20 w-full">
        <div class="flex justify-between items-center">
          <h3 class="text-2xl font-bold">Sasto Sulav Express</h3>
          <button id="closeSidebar">
            <i class="fa fa-times fa-2x" aria-hidden="true"></i>
          </button>
        </div>
        <div class="flex flex-col items-start gap-y-3 w-full">
          <select id="mobileLangSelect" class="bg-transparent border border-[var(--border)] rounded-md p-2 w-full">
            <option value="ja">日本語</option>
            <option value="en">English</option>
          </select>
          <ul class="font-medium text-sm space-y-6" id="sidebarLinks">
            <li>
              <a data-i18n="sastoSulav" href="/sasto-sulav-express.html" class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase block w-full">Sasto Sulav Express</a>
            </li>
            <li>
              <a data-i18n="nepaliDining" href="/nepali-dining.html" class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase block w-full">NEPALI Dining</a>
            </li>
            <li>
              <a data-i18n="about" href="/about.html" class="py-2 px-3 rounded-xl hover:bg-[var(--secondary)] uppercase block w-full">ABOUT</a>
            </li>
          </ul>
        </div>
      </div>
      <button data-i18n="close" id="closeSidebarButton" class="py-2 px-4 rounded-md bg-[var(--accent)] w-full">
        CLOSE
      </button>
    </div>
  </div>

  <!-- =========== MAIN CONTENT =============== -->
  <main class="container py-10">
    <!-- ============= PAGE BANNER ============= -->
    <section data-aos="zoom-in">
      <a href="https://tetoteto.co.jp/shop/sasto-sulav-express" target="_blank" class="relative md:h-[60vh] h-[40vh] block rounded-3xl overflow-hidden group">
        <div class="h-full w-full bg-white/50 absolute inset-0 opacity-0 group-hover:opacity-100 transition-all ease-in-out duration-500 flex items-center justify-center z-50">
          <div class="flex items-center justify-center gap-2 translate-y-6 group-hover:translate-y-0 transition-all ease-in-out duration-500 delay-150">
            <p class="md:text-3xl text-xl text-[var(--muted-foreground)] font-lexend" data-i18n="shopNow">
              Shop Now
            </p>
            <i class="fa fa-arrow-right text-[var(--muted-foreground)] group-hover:translate-x-3 transition-all ease-in-out duration-500 delay-200 fa-2x" aria-hidden="true"></i>
          </div>
        </div>
        <img src="https://images.unsplash.com/photo-1607349913338-fca6f7fc42d0?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="banner" class="h-full w-full" />
      </a>
    </section>

    <!-- ============== SHOP DESCRIPTION ============= -->
    <article class="grid md:grid-cols-2 gap-8 md:gap-12 lg:gap-16 py-10 items-start">
      <blockquote data-aos="fade-right" class="text-3xl md:text-4xl lg:text-5xl font-medium tracking-tight flex items-start gap-x-1 relative">
        <span class="text-emerald-600">&quot;</span>
        <div class="group relative overflow-hidden w-full text-[var(--primary)] bg-[var(--muted)]">
          <span class="invisible">Text Glitch Effect</span>
          <div class="absolute left-0 top-0 transition duration-300 ease-in-out group-hover:-translate-y-[120%]">
            <a href="https://tetoteto.co.jp/shop/sasto-sulav-express" target="_blank" class="flex items-center gap-x-3 group">
              <p data-i18n="shopNow">Shop Now</p>
              <i class="fa fa-arrow-right scale-100 text-base" aria-hidden="true"></i>
            </a>
          </div>
          <a href="https://tetoteto.co.jp/shop/sasto-sulav-express" target="_blank" class="absolute left-0 top-0 translate-y-full transition duration-300 ease-in-out group-hover:translate-y-0" data-i18n="sastoSulavExpress">
            Sasto Sulav Express
          </a>
        </div>
      </blockquote>
      <div class="relative" data-aos="fade-left">
        <p class="text-lg md:text-xl text-[var(--muted-foreground)] leading-relaxed" data-i18n="sastoSulavDesc2">
          Sasto Sulav, your one-stop grocery store in Japan, offers fresh
          produce, quality goods, and unbeatable prices to make every shopping
          trip convenient and affordable for you.
        </p>
      </div>
    </article>

    <!-- =========== SHOP ITEMS ========= -->
    <div class="pt-16 space-y-6">
      <div class="flex items-center justify-between">
        <h3 class="md:text-3xl text-2xl font-semibold text-[var(--secondary-foreground)]" data-i18n="featuredProducts">
          Featured Products
        </h3>
        <a href="https://tetoteto.co.jp/shop/sasto-sulav-express" target="_blank" class="text-[var(--accent-foreground)] flex items-center gap-x-1 hover:bg-gray-100 px-4 h-10 rounded-md sm:text-base text-sm">
          Visit Shop <i class="fa fa-arrow-right" aria-hidden="true"></i>
        </a>
      </div>

      <!-- ITEMS CAROUSEL -->
      <div class="embla overflow-hidden relative">
        <div class="embla__viewport">
          <div class="embla__container flex gap-4">
            <a href="https://tetoteto.co.jp/item/wai-wai-masala-curry" target="_blank" rel="noreferrer" class="embla__slide shadow border border-[var(--border)] rounded-xl group hover:border-[var(--primary)]">
              <div class="h-56 p-0 rounded-t-xl overflow-hidden relative">
                <img src="https://cdn.tetoteto.co.jp/item-images/a303750/a303750_1677146788675_thumbnail.jpg" alt="wai wai masala curry" class="object-contain h-full w-full group-hover:scale-125 transition-all ease-in-out duration-500" />
              </div>
              <div class="p-4 space-y-3">
                <h3 class="text-xl font-semibold">wai wai masala curry</h3>
                <p class="text-[var(--secondary-foreground)] whitespace-nowrap overflow-hidden text-ellipsis">
                  Masala curry falovrs wai wai noodles
                </p>
              </div>
            </a>
            <a href="https://tetoteto.co.jp/item/khasiko-khutta-a1401306mgd" target="_blank" rel="noreferrer" class="embla__slide shadow border border-[var(--border)] rounded-xl group hover:border-[var(--primary)]">
              <div class="h-56 p-0 rounded-t-xl overflow-hidden relative">
                <img src="https://cdn.tetoteto.co.jp/item-images/a1401306mgd/8ab76d56-c589-4991-b673-ed26fea5922e.jpg" alt=" Mutton leg, Khasiko khutta 1 KG" class="object-contain h-full w-full group-hover:scale-125 transition-all ease-in-out duration-500" />
              </div>
              <div class="p-4 space-y-3">
                <h3 class="text-xl font-semibold">
                  Mutton leg, Khasiko khutta 1 KG
                </h3>
                <p class="text-[var(--secondary-foreground)] whitespace-nowrap overflow-hidden text-ellipsis">
                  It is used for making soup and it is consider to be very
                  nutrition rich and it is cooked along with moong daal for
                  extra nutrition.
                </p>
              </div>
            </a>
            <a href="https://tetoteto.co.jp/item/gold-basmati-rice-5kg-a6747689upc" target="_blank" rel="noreferrer" class="embla__slide shadow border border-[var(--border)] rounded-xl group hover:border-[var(--primary)]">
              <div class="h-56 p-0 rounded-t-xl overflow-hidden relative">
                <img src="https://cdn.tetoteto.co.jp/item-images/a6747689upc/274d57dc-4e01-4e33-95a3-d30d7f3939c5.jpg" alt="Gold Basmati Rice 5kg" class="object-contain h-full w-full group-hover:scale-125 transition-all ease-in-out duration-500" />
              </div>
              <div class="p-4 space-y-3">
                <h3 class="text-xl font-semibold">Gold Basmati Rice 5kg</h3>
                <p class="text-[var(--secondary-foreground)] whitespace-nowrap overflow-hidden text-ellipsis">
                  Padma gold basmati rice is a aromatic long grain rice
                  perfect for cooking biryani and pulau.
                </p>
              </div>
            </a>
            <a href="https://tetoteto.co.jp/item/mix-dal-a3966092mqa" target="_blank" rel="noreferrer" class="embla__slide shadow border border-[var(--border)] rounded-xl group hover:border-[var(--primary)]">
              <div class="h-56 p-0 rounded-t-xl overflow-hidden relative">
                <img src="https://cdn.tetoteto.co.jp/item-images/a3966092mqa/8ac413aa-2dec-497f-b7fb-b0bd3c42aea9.jpg" alt="Mix Dal Padma 1 kg" class="object-contain h-full w-full group-hover:scale-125 transition-all ease-in-out duration-500" />
              </div>
              <div class="p-4 space-y-3">
                <h3 class="text-xl font-semibold">Mix Dal Padma 1 kg</h3>
                <p class="text-[var(--secondary-foreground)] whitespace-nowrap overflow-hidden text-ellipsis">
                  mix dal from padma. it is very tasty and good for health.
                </p>
              </div>
            </a>
            <a href="https://tetoteto.co.jp/item/kala-chana-1kg-a936319" target="_blank" rel="noreferrer" class="embla__slide shadow border border-[var(--border)] rounded-xl group hover:border-[var(--primary)]">
              <div class="h-56 p-0 rounded-t-xl overflow-hidden relative">
                <img src="https://cdn.tetoteto.co.jp/item-images/a936319/696a6108-c9c9-4f21-9123-4b703f1b9197.jpg" alt="Kala Chana, black chana Ambika" class="object-contain h-full w-full group-hover:scale-125 transition-all ease-in-out duration-500" />
              </div>
              <div class="p-4 space-y-3">
                <h3 class="text-xl font-semibold">
                  Kala Chana, black chana Ambika
                </h3>
                <p class="text-[var(--secondary-foreground)] whitespace-nowrap overflow-hidden text-ellipsis">
                  kala chana (black chickpea) is highly nutritious and is
                  eaten in curries, soups and salads. I’ve
                </p>
              </div>
            </a>
            <a href="https://tetoteto.co.jp/item/fried-onion-500gm-a5800323rtu" target="_blank" rel="noreferrer" class="embla__slide shadow border border-[var(--border)] rounded-xl group hover:border-[var(--primary)]">
              <div class="h-56 p-0 rounded-t-xl overflow-hidden relative">
                <img src="https://cdn.tetoteto.co.jp/item-images/a5800323rtu/c59de8d1-26a6-44fb-a215-a25ffe610cae.jpg" alt="Fried Onion 500gm" class="object-contain h-full w-full group-hover:scale-125 transition-all ease-in-out duration-500" />
              </div>
              <div class="p-4 space-y-3">
                <h3 class="text-xl font-semibold">Fried Onion 500gm</h3>
                <p class="text-[var(--secondary-foreground)] whitespace-nowrap overflow-hidden text-ellipsis">
                  fried onjion are used to garnish most of the indian dish
                  including Biryani, korma and meat curry
                </p>
              </div>
            </a>
            <a href="https://tetoteto.co.jp/item/fresh-mutton-1-kg-a8885222dff" target="_blank" rel="noreferrer" class="embla__slide shadow border border-[var(--border)] rounded-xl group hover:border-[var(--primary)]">
              <div class="h-56 p-0 rounded-t-xl overflow-hidden relative">
                <img src="https://cdn.tetoteto.co.jp/item-images/a8885222dff/ffae557a-52d5-457d-86ec-95ba731dd0ed.jpg" alt="Fresh Mutton 1kg, khasi ko masu" class="object-contain h-full w-full group-hover:scale-125 transition-all ease-in-out duration-500" />
              </div>
              <div class="p-4 space-y-3">
                <h3 class="text-xl font-semibold">
                  Fresh Mutton 1kg, khasi ko masu
                </h3>
                <p class="text-[var(--secondary-foreground)] whitespace-nowrap overflow-hidden text-ellipsis">
                  fresh mutton 1 KG, not the one from frozen, fresh mutton
                  meat , same taste like Nepal, including bone, no smell to
                  worry
                </p>
              </div>
            </a>
            <a href="https://tetoteto.co.jp/item/aamako-dalle-tama-achar-a8944242dls" target="_blank" rel="noreferrer" class="embla__slide shadow border border-[var(--border)] rounded-xl group hover:border-[var(--primary)]">
              <div class="h-56 p-0 rounded-t-xl overflow-hidden relative">
                <img src="https://cdn.tetoteto.co.jp/item-images/a8944242dls/ddf4cd73-5251-4c50-a4b1-f272ac0880b1.jpg" alt="Aamako Dalle & Tama Achar" class="object-contain h-full w-full group-hover:scale-125 transition-all ease-in-out duration-500" />
              </div>
              <div class="p-4 space-y-3">
                <h3 class="text-xl font-semibold">
                  Aamako Dalle & Tama Achar
                </h3>
                <p class="text-[var(--secondary-foreground)] whitespace-nowrap overflow-hidden text-ellipsis">
                  Aama ko tama and & dalla achar
                </p>
              </div>
            </a>
            <a href="https://tetoteto.co.jp/item/chicken-sausage-a8849092iqv" target="_blank" rel="noreferrer" class="embla__slide shadow border border-[var(--border)] rounded-xl group hover:border-[var(--primary)]">
              <div class="h-56 p-0 rounded-t-xl overflow-hidden relative">
                <img src="https://cdn.tetoteto.co.jp/item-images/a8849092iqv/cfedfefc-484b-4cea-bf7a-55e0a810f7d1.jpg" alt="Chicken sausage " class="object-contain h-full w-full group-hover:scale-125 transition-all ease-in-out duration-500" />
              </div>
              <div class="p-4 space-y-3">
                <h3 class="text-xl font-semibold">Chicken sausage</h3>
                <p class="text-[var(--secondary-foreground)] whitespace-nowrap overflow-hidden text-ellipsis">
                  This is a chicken sausage which is less oiler than pork
                  sausage.
                </p>
              </div>
            </a>
            <a href="https://tetoteto.co.jp/item/masoor-dal-a358332" target="_blank" rel="noreferrer" class="embla__slide shadow border border-[var(--border)] rounded-xl group hover:border-[var(--primary)]">
              <div class="h-56 p-0 rounded-t-xl overflow-hidden relative">
                <img src="https://cdn.tetoteto.co.jp/item-images/a358332/7c482093-3c06-424a-b25e-fd69acf3819a.jpg" alt="Musuro Dal, मुसुरो दाल Ambika" class="object-contain h-full w-full group-hover:scale-125 transition-all ease-in-out duration-500" />
              </div>
              <div class="p-4 space-y-3">
                <h3 class="text-xl font-semibold">
                  Musuro Dal, मुसुरो दाल Ambika
                </h3>
                <p class="text-[var(--secondary-foreground)] whitespace-nowrap overflow-hidden text-ellipsis">
                  split Lintels from beans very popular in every dishes no
                  matter what and when Disclaimer: Please avoid the bulk order
                  of single item.
                </p>
              </div>
            </a>
          </div>
        </div>
        <!-- ========== CAROUSEL NAVIGATION ========= -->
        <button class="embla__prev bg-[var(--background)] text-white px-4 py-2 rounded-lg hover:bg-[var(--accent)] border border-[var(--border)] transition-colors disabled:opacity-50 disabled:cursor-not-allowed absolute top-1/2 left-4 -translate-y-1/2">
          <i class="fa fa-arrow-left text-black" aria-hidden="true"></i>
        </button>
        <button class="embla__next bg-[var(--background)] text-white px-4 py-2 rounded-lg hover:bg-[var(--accent)] border border-[var(--border)] transition-colors disabled:opacity-50 disabled:cursor-not-allowed absolute top-1/2 right-4 -translate-y-1/2">
          <i class="fa fa-arrow-right text-black" aria-hidden="true"></i>
        </button>

        <!-- ========== CAROUSEL PROGRESS ========= -->
        <div class="flex justify-center pt-4">
          <div class="embla__progress">
            <div class="embla__progress__bar" style="transform: translate3d(0%, 0px, 0px)"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- ========== ABOUT SHOP ========= -->
    <article class="pt-28 space-y-6">
      <h3 class="text-3xl font-semibold text-[var(--secondary-foreground)]" data-i18n="about">
        About
      </h3>
      <div class="grid grid-cols-12 items-start gap-6">
        <div class="md:col-span-8 col-span-12">
          <!-- ============= SHOP INFO =============-->
          <div class="space-y-6 text-pretty md:text-base text-sm">
            <p data-i18n="sastoSulavWelcome">
              Welcome to Sasto Sulav Express, your trusted source for
              authentic South Asian groceries in Japan. We take pride in
              offering a diverse selection of high-quality products imported
              directly from Nepal, India, and surrounding regions. Our store
              specializes in essential ingredients for South Asian cuisine,
              including spices, lentils, rice, fresh produce, and traditional
              snacks. We understand the importance of finding familiar tastes
              from home, which is why we carefully curate our product
              selection to meet the needs of our community.
            </p>
            <p data-i18n="aboutSastoSulav">
              At Sasto Sulav Express, we believe in making quality products
              accessible and affordable. Our commitment to customer
              satisfaction extends beyond just providing products - we offer
              competitive prices, reliable delivery services, and expert
              advice on product selection. Whether you're looking for everyday
              essentials or specialty items for festivals and celebrations,
              our extensive inventory ensures you'll find exactly what you
              need. Visit our store today and experience the convenience of
              shopping for your favorite South Asian groceries in Japan.
            </p>
          </div>
        </div>
        <!-- ============== SHOP CARD ====================== -->
        <div data-aos="flip-right" class="md:col-span-4 col-span-12">
          <div class="rounded-3xl bg-gray-200 border hover:border-[var(--primary)] border-transparent p-4">
            <a href="https://tetoteto.co.jp/shop/sasto-sulav-express" target="_blank" class="block">
              <div class="flex-row items-center gap-4 p-4 flex">
                <div class="h-14 w-14 border-4 rounded-full border-[var(--primary)] relative overflow-hidden">
                  <img fill class="object-cover" alt="sasto-sulav-express" src="https://cdn.tetoteto.co.jp/item-images/shop-image/2586825067/2635395597_1.jpg" />
                </div>
                <div class="space-y-1">
                  <h3 class="text-lg font-semibold" data-i18n="sastoSulavExpress">
                    Sasto Sulav Express
                  </h3>
                  <p class="text-[var(--muted-foreground)] uppercase font-medium text-sm" data-i18n="groceryStore">
                    Grocery Store
                  </p>
                </div>
              </div>
              <div>
                <div class="py-6 leading-7 font-medium text-pretty">
                  <p data-i18n="sastoSulavCardDesc">
                    Sasto Sulav provides fresh groceries, quality products,
                    and everyday essentials in Japan. Offering affordability
                    and convenience, it ensures a delightful shopping
                    experience for all customers.
                  </p>
                </div>
              </div>
            </a>
            <div class="block space-y-3">
              <div class="flex items-center gap-x-3">
                <i class="fa fa-map-marker" aria-hidden="true"></i>

                <p class="text-lg">Japan, Asia</p>
              </div>
              <div class="flex items-center gap-x-4 text-xl">
                <a href="https://www.facebook.com/sastosulavstore" target="_blank">
                  <i class="fa fa-facebook" aria-hidden="true"></i>
                </a>
                <a href="https://www.tiktok.com/@sastosulabexpress" target="_blank">
                  <i class="fa fa-music" aria-hidden="true"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </article>
  </main>

  <!-- =========== FOOTER =============== -->
  <footer id="footer" class="bg-[var(--muted)] py-4 text-center"></footer>
  <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
  <script>
    const emblaNode = document.querySelector(".embla");
    const options = {
      loop: false,
      dragFree: true,
      autoplay: {
        delay: 0
      },
      containScroll: false,
      align: "start",
    };

    const viewportNode = emblaNode.querySelector(".embla__viewport");
    const prevBtnNode = document.querySelector(".embla__prev");
    const nextBtnNode = document.querySelector(".embla__next");
    const plugins = [EmblaCarouselAutoplay()];
    const emblaApi = EmblaCarousel(viewportNode, options, plugins);

    const addTogglePrevNextBtnsActive = (emblaApi, prevBtn, nextBtn) => {
      const togglePrevNextBtnsState = () => {
        if (emblaApi.canScrollPrev()) prevBtn.removeAttribute("disabled");
        else prevBtn.setAttribute("disabled", "disabled");

        if (emblaApi.canScrollNext()) nextBtn.removeAttribute("disabled");
        else nextBtn.setAttribute("disabled", "disabled");
      };

      emblaApi
        .on("select", togglePrevNextBtnsState)
        .on("init", togglePrevNextBtnsState)
        .on("reInit", togglePrevNextBtnsState);

      return () => {
        prevBtn.removeAttribute("disabled");
        nextBtn.removeAttribute("disabled");
      };
    };

    const addPrevNextBtnsClickHandlers = (emblaApi, prevBtn, nextBtn) => {
      const scrollPrev = () => {
        emblaApi.scrollPrev();
      };
      const scrollNext = () => {
        emblaApi.scrollNext();
      };
      prevBtn.addEventListener("click", scrollPrev, false);
      nextBtn.addEventListener("click", scrollNext, false);

      const removeTogglePrevNextBtnsActive = addTogglePrevNextBtnsActive(
        emblaApi,
        prevBtn,
        nextBtn
      );

      return () => {
        removeTogglePrevNextBtnsActive();
        prevBtn.removeEventListener("click", scrollPrev, false);
        nextBtn.removeEventListener("click", scrollNext, false);
      };
    };

    const removePrevNextBtnsClickHandlers = addPrevNextBtnsClickHandlers(
      emblaApi,
      prevBtnNode,
      nextBtnNode
    );
    const progressNode = emblaNode.querySelector(".embla__progress__bar");

    const updateProgress = () => {
      const progress = Math.max(0, Math.min(1, emblaApi.scrollProgress()));
      progressNode.style.transform = `translateX(${progress * 100}%)`;
    };

    emblaApi.on("scroll", updateProgress);
    emblaApi.on("reInit", updateProgress);
    emblaApi.on("destroy", removePrevNextBtnsClickHandlers);
  </script>

  <script src="/script.js"></script>
</body>

</html>